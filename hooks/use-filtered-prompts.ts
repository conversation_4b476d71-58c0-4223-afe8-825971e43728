"use client"

import { useState, useEffect } from "react"
import { getPrompts } from "@/lib/api-services"
import type { PromptCard } from "@/lib/types"

interface UseFilteredPromptsProps {
  categorySlugs?: string[]
  toolSlugs?: string[]
  tagSlugs?: string[]
  aiModelSlugs?: string[] // <-- Add aiModelSlugs prop
  searchQuery?: string
  currentUserId?: string // NEW: for saved status
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

export function useFilteredPrompts({
  categorySlugs,
  toolSlugs,
  tagSlugs,
  aiModelSlugs,
  searchQuery,
  currentUserId,
  sortBy = "created_at",
  sortOrder = "desc",
}: UseFilteredPromptsProps) {
  const [prompts, setPrompts] = useState<PromptCard[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [hasMore, setHasMore] = useState(true) // Add hasMore state if you plan pagination
  const [page, setPage] = useState(0) // Add page state for pagination
  const PROMPTS_PER_PAGE = 20 // Define how many prompts to load per "page"

  // Reset prompts and pagination when filters or search query change
  useEffect(() => {
    setPrompts([])
    setPage(0)
    setHasMore(true)
    setIsLoading(true)
    setError(null)
  }, [categorySlugs, toolSlugs, tagSlugs, aiModelSlugs, searchQuery, currentUserId, sortBy, sortOrder])

  // Effect to fetch prompts based on current filters, search, and page
  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const fetchPrompts = async () => {
      // Don't fetch if already loading
      if (isLoading && page > 0) return;

      console.log("[useFilteredPrompts] Starting fetch with params:", {
        categorySlugs,
        toolSlugs,
        tagSlugs,
        aiModelSlugs,
        searchQuery,
        currentUserId,
        currentUserIdType: typeof currentUserId,
        currentUserIdValid: currentUserId ? /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(currentUserId) : 'N/A',
        sortBy,
        sortOrder,
        page,
        offset: page * PROMPTS_PER_PAGE,
        timestamp: new Date().toISOString()
      })

      setIsLoading(true)
      try {
        // Check if request was aborted before making API call
        if (signal.aborted) {
          console.log("[useFilteredPrompts] Request aborted before API call");
          return;
        }

        console.log("[useFilteredPrompts] Starting API call with params:", {
          categorySlugs: categorySlugs?.length || 0,
          toolSlugs: toolSlugs?.length || 0,
          tagSlugs: tagSlugs?.length || 0,
          aiModelSlugs: aiModelSlugs?.length || 0,
          searchQuery: searchQuery ? `"${searchQuery.substring(0, 30)}..."` : null,
          currentUserId: currentUserId ? `${currentUserId.substring(0, 8)}...` : null,
          sortBy,
          sortOrder,
          page,
          offset: page * PROMPTS_PER_PAGE,
          hasSignal: !!signal,
          signalAborted: signal?.aborted || false
        });

        // Validate currentUserId format if provided
        if (currentUserId) {
          const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
          if (!uuidRegex.test(currentUserId)) {
            console.error("[useFilteredPrompts] Invalid currentUserId format:", currentUserId);
            setError(new Error("Invalid user ID format"));
            return;
          }
        }

        const offset = page * PROMPTS_PER_PAGE
        console.log("[useFilteredPrompts] About to call getPrompts...");
        const data = await getPrompts({
          categorySlugs,
          toolSlugs,
          tagSlugs,
          aiModelSlugs,
          searchQuery,
          currentUserId, // NEW: Pass current user ID
          limit: PROMPTS_PER_PAGE,
          offset: offset,
          sortBy,
          sortOrder,
          signal, // Pass abort signal to API call
        })
        console.log("[useFilteredPrompts] getPrompts call completed, data length:", data?.length || 0);

        console.log("[useFilteredPrompts] Fetch completed:", {
          dataLength: data.length,
          page,
          hasMore: data.length === PROMPTS_PER_PAGE
        })

        // Check if request was aborted before updating state
        if (!signal.aborted) {
          setPrompts((prev) => (page === 0 ? data : [...prev, ...data]))
          setHasMore(data.length === PROMPTS_PER_PAGE)
          setError(null)
        }
      } catch (err) {
        console.error("[useFilteredPrompts] Fetch error:", {
          error: err,
          message: err instanceof Error ? err.message : 'Unknown error',
          errorType: typeof err,
          errorKeys: err && typeof err === 'object' ? Object.keys(err) : [],
          stack: err instanceof Error ? err.stack : 'No stack trace',
          currentUserId,
          page,
          offset: page * PROMPTS_PER_PAGE,
          timestamp: new Date().toISOString()
        })

        // Only update state if request wasn't aborted
        if (!signal.aborted) {
          // Create a more user-friendly error message
          let userFriendlyError: Error;

          if (err instanceof Error) {
            if (err.message.includes("Database configuration is missing")) {
              userFriendlyError = new Error("Database connection failed. Please refresh the page and try again.");
            } else if (err.message.includes("connection")) {
              userFriendlyError = new Error("Network connection error. Please check your internet connection and try again.");
            } else {
              userFriendlyError = err;
            }
          } else {
            userFriendlyError = new Error("An unexpected error occurred while loading prompts. Please try again.");
          }

          setError(userFriendlyError);
        }
      } finally {
        // Only update loading state if request wasn't aborted
        if (!signal.aborted) {
          setIsLoading(false)
        }
      }
    }

    fetchPrompts()

    return () => {
      controller.abort();
    }
    // Re-fetch when filters, search query, or page change
  }, [categorySlugs, toolSlugs, tagSlugs, aiModelSlugs, searchQuery, currentUserId, sortBy, sortOrder, page])

  // Function to load the next page of results
  const loadMore = () => {
    if (!isLoading && hasMore) {
      setPage((prevPage) => prevPage + 1)
    }
  }

  return { prompts, isLoading, error, hasMore, loadMore } // Return pagination states/functions
}
