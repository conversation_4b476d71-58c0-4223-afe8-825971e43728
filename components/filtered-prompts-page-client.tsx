"use client"

import { useEffect, useState, use<PERSON><PERSON>back, useRef, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Head from 'next/head'; // Import Head for meta tags
import { getCategoryBySlug, getToolBySlug, getTagBySlug } from "lib/api-services" // Keep these for potential future use if needed, but initial data is passed via props
import PromptGrid from "components/prompt-grid"
import CategoryFilters from "components/category-filters"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "components/ui/select"
import { useFilteredPrompts } from "hooks/use-filtered-prompts"
import type { Category, Tool, Tag } from "lib/types"
import { useUser } from "@/lib/hooks/use-user"
import { Loader2, Grid3X3, List } from "lucide-react" // Added Grid3X3 and List
import { Button } from "components/ui/button"
 
 interface FilteredPromptsPageClientProps {
  initialSlug: string
  entityType: "category" | "tool" | "tag" | "search"
  initialEntityData: Category | Tool | Tag | { name: string; prompts: any[] } | null // Added type for search results
  entityName: string // e.g., "Category", "Tool", "Tag"
}

export default function FilteredPromptsPageClient({
  initialSlug,
  entityType,
  initialEntityData,
  entityName,
}: FilteredPromptsPageClientProps) {
  console.log("[FilteredPromptsPageClient] Component rendering with props:", {
    initialSlug,
    entityType,
    initialEntityData: initialEntityData ? 'provided' : 'null',
    entityName
  });

  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useUser() // ADD: Get current user



  // State for the main entity (passed from server)
  // Use a more generic type for entity to accommodate the search entity type
  const [entity, setEntity] = useState<any>(initialEntityData)
  const [isLoadingEntity, setIsLoadingEntity] = useState(!initialEntityData) // Only load if not passed initially
  const [errorEntity, setErrorEntity] = useState<Error | null>(null)

  // Initialize state based on URL params and entity type
  const initializeFilters = () => {
    const initialCategories = searchParams.get("categories")?.split(",").filter(Boolean) || []
    const initialTags = searchParams.get("tags")?.split(",").filter(Boolean) || []
    const initialTools = searchParams.get("tools")?.split(",").filter(Boolean) || []
    const initialModels = searchParams.get("models")?.split(",").filter(Boolean) || []
    const initialSearch = searchParams.get("q") || ""
    const initialSortBy = searchParams.get("sort") || "trending"
    const initialTimePeriod = searchParams.get("time") || "all"
    const initialView = (searchParams.get("view") as "grid" | "list") || "grid"

    // Set the primary entity filter based on entityType
    let categories: string[] = []
    let tags: string[] = []
    let tools: string[] = []

    if (entityType === "category") {
      categories = initialCategories.length > 0 ? initialCategories : [initialSlug]
      tags = initialTags
      tools = initialTools
    } else if (entityType === "tool") {
      tools = initialTools.length > 0 ? initialTools : [initialSlug]
      categories = initialCategories
      tags = initialTags
    } else if (entityType === "tag") {
      tags = initialTags.length > 0 ? initialTags : [initialSlug]
      categories = initialCategories
      tools = initialTools
    }

    return {
      categories,
      tags,
      tools,
      models: initialModels,
      search: initialSearch,
      sortBy: initialSortBy as "trending" | "top" | "latest",
      timePeriod: initialTimePeriod as "week" | "month" | "all",
      viewMode: initialView
    }
  }

  const initialFilters = initializeFilters()

  // State for filters managed by this component
  const [selectedCategories, setSelectedCategories] = useState<string[]>(initialFilters.categories)
  const [selectedTags, setSelectedTags] = useState<string[]>(initialFilters.tags)
  const [selectedTools, setSelectedTools] = useState<string[]>(initialFilters.tools)
  const [selectedModels, setSelectedModels] = useState<string[]>(initialFilters.models)
  const [filterSearchTerm, setFilterSearchTerm] = useState<string>(initialFilters.search)
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>(initialFilters.search)

  // State for sorting and time period
  const [sortBy, setSortBy] = useState<"trending" | "top" | "latest">(initialFilters.sortBy)
  const [timePeriod, setTimePeriod] = useState<"week" | "month" | "all">(initialFilters.timePeriod)
  const [viewMode, setViewMode] = useState<"grid" | "list">(initialFilters.viewMode)






  // ---- Debounce Search Term ----
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(filterSearchTerm)
    }, 300)
    return () => clearTimeout(handler)
  }, [filterSearchTerm])

  const buildSearchParams = useCallback(() => {
    const params = new URLSearchParams()
    if (selectedCategories.length > 0 && !(entityType === 'category' && selectedCategories.length === 1 && selectedCategories[0] === initialSlug)) {
        params.set("categories", selectedCategories.join(","))
    }
    if (selectedTags.length > 0 && !(entityType === 'tag' && selectedTags.length === 1 && selectedTags[0] === initialSlug)) {
      params.set("tags", selectedTags.join(","))
    }
    if (selectedTools.length > 0 && !(entityType === 'tool' && selectedTools.length === 1 && selectedTools[0] !== initialSlug)) { // Corrected logic for tools
      params.set("tools", selectedTools.join(","))
    }
    if (selectedModels.length > 0) params.set("models", selectedModels.join(","))
    if (debouncedSearchTerm) params.set("q", debouncedSearchTerm)
    if (sortBy !== "trending") params.set("sort", sortBy)
    if (timePeriod !== "all") params.set("time", timePeriod)
    if (viewMode !== "grid") params.set("view", viewMode)
    return params.toString()
  }, [entityType, initialSlug, selectedCategories, selectedTags, selectedTools, selectedModels, debouncedSearchTerm, sortBy, timePeriod, viewMode]);

  // ---- Fetch Entity Data (if not provided initially) ----
  // This might be redundant if initialEntityData is always passed, but good fallback
  useEffect(() => {
    if (entity || !initialSlug || !isLoadingEntity) return;

    const fetchEntity = async () => {
      try {
        let data = null;
        if (entityType === 'category') data = await getCategoryBySlug(initialSlug);
        else if (entityType === 'tool') data = await getToolBySlug(initialSlug);
        else if (entityType === 'tag') data = await getTagBySlug(initialSlug);
        setEntity(data);
      } catch (err) {
        setErrorEntity(err instanceof Error ? err : new Error("Unknown error occurred"));
      } finally {
        setIsLoadingEntity(false);
      }
    };
    fetchEntity();
  }, [initialSlug, entityType, entity, isLoadingEntity]);


  // ---- Update URL Effect ----
  // Update URL when filter state changes
  const isMounted = useRef(false);
  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true;
      return;
    }

    const newSearch = buildSearchParams();
    // Adjust the base path based on entityType
    const currentPath = entityType === 'search' ? '/search' : `/${entityType}/${initialSlug}`;
    const newUrl = newSearch ? `${currentPath}?${newSearch}` : currentPath;

    // Use replace to avoid polluting browser history with filter changes
    if (window.location.pathname + window.location.search !== newUrl) {
      router.replace(newUrl, { scroll: false })
    }
  }, [selectedCategories, selectedTags, selectedTools, selectedModels, initialSlug, entityType, router, sortBy, timePeriod, viewMode, debouncedSearchTerm, buildSearchParams])

  // Convert frontend sort values to API sort values
  const getApiSortParams = () => {
    switch (sortBy) {
      case "trending":
        return { sortBy: "trending_score", sortOrder: "desc" as const }
      case "top":
        return { sortBy: "rating", sortOrder: "desc" as const }
      case "latest":
        return { sortBy: "created_at", sortOrder: "desc" as const }
      default:
        return { sortBy: "created_at", sortOrder: "desc" as const }
    }
  }

  const { sortBy: apiSortBy, sortOrder: apiSortOrder } = getApiSortParams()

  // ---- Fetch Prompts Hook ----
  const {
    prompts,
    isLoading: isLoadingPrompts,
    error: errorPrompts,
    hasMore,
    loadMore: loadMorePrompts,
  } = useFilteredPrompts({
    // Pass the current state of filters to the hook
    categorySlugs: selectedCategories,
    tagSlugs: selectedTags,
    toolSlugs: selectedTools,
    aiModelSlugs: selectedModels,
    searchQuery: debouncedSearchTerm, // Use debounced term for fetching
    currentUserId: user?.id, // NEW: Pass current user ID
    sortBy: apiSortBy,
    sortOrder: apiSortOrder,
  })

  // Debug logging (can be removed in production)
  if (process.env.NODE_ENV === 'development') {
    console.log("[FilteredPromptsPageClient] Debug info:", {
      entityType,
      initialSlug,
      selectedCategories,
      selectedTags,
      selectedTools,
      selectedModels,
      debouncedSearchTerm,
      currentUserId: user?.id,
      promptsLength: prompts.length,
      isLoadingPrompts,
      errorPrompts: errorPrompts?.message,
      hasMore,
      sortBy,
      timePeriod,
      apiSortBy,
      apiSortOrder
    });
  }

  // ---- Client-side filtering by time period ----
  const filterPromptsByTime = (promptsToFilter: any[], timePeriod: string) => {
      const now = new Date();
      return promptsToFilter.filter((prompt) => {
          // Handle both camelCase and snake_case property names
          const promptDate = new Date(prompt.createdAt || prompt.created_at);
          if (timePeriod === 'week') {
              const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // Fixed date calculation
              return promptDate >= oneWeekAgo;
          } else if (timePeriod === 'month') {
              const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // Fixed date calculation
              return promptDate >= oneMonthAgo;
          } else { // 'all'
              return true;
          }
      });
  };

  // ---- Client-side sorting ----
  const sortedAndFilteredPrompts = useMemo(() => {
      // Apply time period filter first
      const timeFiltered = filterPromptsByTime(prompts, timePeriod);

      // Then sort based on selected option
      if (sortBy === "trending") {
          // Handle both camelCase and snake_case property names
          return [...timeFiltered].sort((a, b) => {
              const aTrending = a.trendingScore || a.trending_score || 0;
              const bTrending = b.trendingScore || b.trending_score || 0;
              const aDate = new Date(a.createdAt || a.created_at).getTime();
              const bDate = new Date(b.createdAt || b.created_at).getTime();
              return (bTrending || bDate) - (aTrending || aDate);
          });
      } else if (sortBy === "top") {
          // Sort by rating
          return [...timeFiltered].sort((a, b) => (b.rating || 0) - (a.rating || 0));
      } else {
          // Sort by date (latest)
          return [...timeFiltered].sort((a, b) => {
              const aDate = new Date(a.createdAt || a.created_at).getTime();
              const bDate = new Date(b.createdAt || b.created_at).getTime();
              return bDate - aDate;
          });
      }
  }, [prompts, timePeriod, sortBy]);

  // Debug the client-side filtering (can be removed in production)
  if (process.env.NODE_ENV === 'development') {
    console.log("[FilteredPromptsPageClient] Client-side filtering debug:", {
      originalPromptsLength: prompts.length,
      timePeriod,
      sortBy,
      filteredPromptsLength: sortedAndFilteredPrompts.length
    });
  }

  // ---- Clear Filters Handler ----
  const handleClearAllFilters = () => {
    // Reset filters, keeping the initial entity type selected
    if (entityType === 'category') {
        setSelectedCategories([initialSlug]);
        setSelectedTags([]);
        setSelectedTools([]);
        setSelectedModels([]);
    } else if (entityType === 'tool') {
        setSelectedTools([initialSlug]);
        setSelectedCategories([]);
        setSelectedTags([]);
        setSelectedModels([]);
    } else if (entityType === 'tag') {
        setSelectedTags([initialSlug]);
        setSelectedCategories([]);
        setSelectedTools([]);
        setSelectedModels([]);
    }
    setFilterSearchTerm(""); // Clear the search term as well
    setSortBy("trending"); // Reset sort as well
    setTimePeriod("all"); // Reset time period as well
    setViewMode("grid"); // Reset view mode as well
  };

  // ---- Infinite Scroll Observer ----
  const observer = useRef<IntersectionObserver>(null)
  const lastPromptElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (isLoadingPrompts) return; // Don't observe if already loading
      if (observer.current) observer.current.disconnect()

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          console.log("Infinite scroll triggered")
          loadMorePrompts()
        }
      })

      if (node) observer.current.observe(node)
    },
    [isLoadingPrompts, hasMore, loadMorePrompts],
  )

  // ---- Loading/Error States ----
  if (isLoadingEntity) {
    return (
      <div className="container mx-auto flex min-h-[60vh] items-center justify-center px-4 py-8">
        <Loader2 className="h-12 w-12 animate-spin text-accent-green" />
      </div>
    )
  }

  if (errorEntity || !entity) {
    // Consider redirecting to a not-found page or showing a specific error
    return (
      <div className="container mx-auto px-4 py-8 text-red-500">
        Error loading {entityName}: {errorEntity?.message || `${entityName} not found.`}
      </div>
    )
  }

  // ---- Determine if Filters are Active ----
  const areFiltersActive =
    (entityType !== 'category' && selectedCategories.length > 0) ||
    (entityType !== 'tag' && selectedTags.length > 0) ||
    (entityType !== 'tool' && selectedTools.length > 0) ||
    selectedModels.length > 0 ||
    filterSearchTerm !== "" ||
    sortBy !== "trending" || // Include sorting in active filters check
    timePeriod !== "all"; // Include time period in active filters check


  // ---- Determine if SEO tags are needed ----
  const needsSeoTags = areFiltersActive || // If any secondary filter is active
                       (entityType === 'category' && (selectedCategories.length > 1 || (selectedCategories.length === 1 && selectedCategories[0] !== initialSlug))) ||
                       (entityType === 'tool' && (selectedTools.length > 1 || (selectedTools.length === 1 && selectedTools[0] !== initialSlug))) ||
                       (entityType === 'tag' && (selectedTags.length > 1 || (selectedTags.length === 1 && selectedTags[0] !== initialSlug))) ||
                       debouncedSearchTerm !== "" ||
                       sortBy !== "trending" || // Include sorting in SEO check
                       timePeriod !== "all"; // Include time period in SEO check


  const canonicalUrl = `${process.env.NEXT_PUBLIC_BASE_URL || ''}/${entityType}/${initialSlug}`; // Adjust NEXT_PUBLIC_BASE_URL as needed

  return (
    <>
        {/* Add SEO Meta Tags Dynamically */}
        <Head>
            {needsSeoTags && <meta name="robots" content="noindex, follow" />}
            {needsSeoTags && <link rel="canonical" href={canonicalUrl} />}
            {/* Add other meta tags like title, description if needed, potentially overriding server ones */}
            <title>{entity.name} Prompts {needsSeoTags ? '(Filtered)' : ''} | PromptHQ</title>
            {/* You might want to generate a dynamic description based on filters */}

            <meta name="description" content={`Find the best ${entity.name} prompts. ${needsSeoTags ? 'Filtered results.' : ''}${entityType === 'category' && (entity as Category).description ? ' ' + (entity as Category).description : ''}`} />
        </Head>
        <main className="container mx-auto px-4 py-8">
        <div className="flex flex-col space-y-8">
            <div>
            <h1 className="text-3xl font-bold mb-2">{entity.name} Prompts</h1>
            {entityType === 'category' && (entity as Category).description && <p className="text-muted-foreground">{(entity as Category).description}</p>}
            </div>


            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Filters Sidebar */}
            <div className="lg:col-span-1">
                <div className="sticky top-20 rounded-lg border p-4">
                    {/* Sort and time period dropdowns */}
                    <div className="flex items-center gap-2 mb-4">
                        <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
                            <SelectTrigger
                                className="w-[130px] rounded-md"
                                style={{ borderRadius: "6px" }}
                            >
                                <SelectValue placeholder="Sort by" />
                            </SelectTrigger>
                            <SelectContent
                                className="rounded-lg"
                                style={{ borderRadius: "8px" }}
                            >
                                <SelectItem value="trending">Trending</SelectItem>
                                <SelectItem value="top">Top</SelectItem>
                                <SelectItem value="latest">Latest</SelectItem>
                            </SelectContent>
                        </Select>

                        <Select value={timePeriod} onValueChange={(value) => setTimePeriod(value as any)}>
                            <SelectTrigger
                                className="w-[130px] rounded-md"
                                style={{ borderRadius: "6px" }}
                            >
                                <SelectValue placeholder="Time period" />
                            </SelectTrigger>
                            <SelectContent
                                className="rounded-lg"
                                style={{ borderRadius: "8px" }}
                            >
                                <SelectItem value="week">This Week</SelectItem>
                                <SelectItem value="month">This Month</SelectItem>
                                <SelectItem value="all">All Time</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                <CategoryFilters
                    // Pass relevant initial slugs based on entityType if needed, though state handles it now
                    selectedCategories={selectedCategories}
                    onSelectCategories={setSelectedCategories}
                    selectedTags={selectedTags}
                    onSelectTags={setSelectedTags}
                    selectedTools={selectedTools}
                    onSelectTools={setSelectedTools}
                    selectedModels={selectedModels}
                    onSelectModels={setSelectedModels}
                    searchTerm={filterSearchTerm}
                    onSearchChange={setFilterSearchTerm}
                    onClearAll={handleClearAllFilters}
                    showClearButton={areFiltersActive}
                />
                </div>
            </div>

            {/* Prompt Grid Area */}
            <div className="lg:col-span-3">
                {/* Top Controls: View Toggle and Result Count */}
                <div className="mb-4 flex flex-wrap items-center justify-between gap-x-4 gap-y-2">
                    {/* Left Controls: View */}
                    <div className="flex items-center gap-2 flex-wrap">
                        {/* View Mode Toggle */}
                        <div className="flex items-center rounded-md border">
                            <Button variant="ghost" size="sm" className={`rounded-r-none h-9 ${viewMode === "grid" ? "bg-accent-green/10 text-accent-green" : ""}`} onClick={() => setViewMode("grid")}><Grid3X3 className="h-4 w-4" /><span className="sr-only">Grid</span></Button>
                            <Button variant="ghost" size="sm" className={`rounded-l-none h-9 ${viewMode === "list" ? "bg-accent-green/10 text-accent-green" : ""}`} onClick={() => setViewMode("list")}><List className="h-4 w-4" /><span className="sr-only">List</span></Button>
                        </div>
                    </div>

                    {/* Right Controls: Result Count */}
                    <div className="text-sm text-muted-foreground">
                        {/* Use final count */}
                        {(!isLoadingPrompts || sortedAndFilteredPrompts.length > 0) && ( // Show count if not loading or if results exist
                            `${sortedAndFilteredPrompts.length} ${
                                sortedAndFilteredPrompts.length === 1 ? "result" : "results"
                            }`
                        )}
                    </div>
                </div>

                {errorPrompts ? (
                <div className="text-red-500">Error loading prompts: {errorPrompts.message}</div>
                ) : (
                <>
                    <PromptGrid
                    prompts={sortedAndFilteredPrompts}
                    emptyMessage={
                    debouncedSearchTerm
                    ? `No prompts found matching "${debouncedSearchTerm}" for ${entity.name}.`
                    : `No prompts found matching your criteria for ${entity.name}.`
                    }
                    maxTags={1}
                    viewMode={viewMode} // Pass viewMode to PromptGrid
                    />
                    <div ref={lastPromptElementRef} style={{ height: "1px" }} />

                    {isLoadingPrompts && prompts.length > 0 && (
                    <div className="mt-8 flex justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
                    </div>
                    )}

                    {!hasMore && prompts.length > 0 && prompts.length >= 10 && ( // Only show if there were enough prompts initially
                    <div className="mt-8 text-center text-muted-foreground">
                    You've reached the end.
                    </div>
                    )}
                </>
                )}
                {isLoadingPrompts && prompts.length === 0 && !errorPrompts && (
                <div className="flex h-64 items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
                    <span className="ml-2">Loading prompts...</span>
                </div>
                )}
            </div>
            </div>
        </div>
        </main>
    </>
  )
}
