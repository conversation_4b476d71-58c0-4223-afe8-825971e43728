import { createBrowserClient } from "@supabase/ssr"

// Create a singleton instance to prevent multiple instances
let _supabase: ReturnType<typeof createBrowserClient> | undefined

export const getSupabaseClient = () => {
  if (_supabase) return _supabase

  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error("[getSupabaseClient] Missing environment variables:", {
      url: supabaseUrl ? 'configured' : 'missing',
      key: supabaseAnonKey ? 'configured' : 'missing',
      nodeEnv: process.env.NODE_ENV
    });
    throw new Error("Supabase configuration is missing. Please check your environment variables.");
  }

  try {
    // Create the Supabase client with session persistence
    // The SSR package handles cookie management automatically
    _supabase = createBrowserClient(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>);

    console.log("[getSupabaseClient] Supabase client created successfully");
    return _supabase;
  } catch (error) {
    console.error("[getSupabaseClient] Failed to create Supabase client:", error);
    throw new Error("Failed to initialize database connection");
  }
}

// For backward compatibility - but with error handling
export const supabase = (() => {
  try {
    return getSupabaseClient();
  } catch (error) {
    console.error("[supabase] Failed to get client:", error);
    // Return null instead of throwing to prevent immediate crashes
    return null;
  }
})();
