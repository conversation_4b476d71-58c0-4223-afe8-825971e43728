import type { Prompt, PromptCard, Category, Tool, Collection } from "./types"
import { generateShortId } from "./utils/url-helpers"

/**
 * Transform database prompt to frontend prompt
 */
export function transformPrompt(data: any): Prompt | null {
  if (!data) return null;

  // Log the incoming data to see its structure (VERY HELPFUL FOR DEBUGGING)
  console.log("[transformPrompt] Raw data from Supabase:", JSON.stringify(data, null, 2));

  const stats = data.stats && Array.isArray(data.stats) && data.stats.length > 0 ? data.stats[0] : data.stats; // Supabase might return stats as an array with one item

  // Log the extracted stats object
  console.log("[transformPrompt] Extracted stats object:", JSON.stringify(stats, null, 2));

  return {
    id: data.id,
    shortId: data.short_id, // From prompts table
    short_id: data.short_id, // Preserve original snake_case field for debugging
    slug: data.slug,         // From prompts table (title slug)
    title: data.title,
    description: data.description,
    text: data.prompt_text || data.text || data.prompt, // Ensure correct field for prompt content
    instructions: data.instructions,
    exampleInput: data.example_input,
    exampleOutput: data.example_output_text, // Corrected from example_output to example_output_text
    exampleOutputImageUrl: data.example_output_image_url,
    imageUrl: data.image_url,
    isPublic: data.is_public,
    user_entered_ai_model: data.user_entered_ai_model,

    category: data.category
      ? {
          id: data.category.id,
          name: data.category.name,
          slug: data.category.slug,
        }
      : (data.categories ? { // Fallback if 'category' is nested under 'categories'
          id: data.categories.id,
          name: data.categories.name,
          slug: data.categories.slug,
        } : {
          id: data.category_id, // Fallback to direct ID
          name: "Other",
          slug: "other",
        }),

    tool: data.tool
      ? {
          id: data.tool.id,
          name: data.tool.name,
          slug: data.tool.slug,
        }
      : (data.tools ? { // Fallback if 'tool' is nested under 'tools'
          id: data.tools.id,
          name: data.tools.name,
          slug: data.tools.slug,
        } : undefined), // Or a default tool if necessary

    user: data.user // Assuming 'user' is the alias for the joined profile data
      ? {
          id: data.user.id,
          username: data.user.username || "Anonymous",
          avatar_url: data.user.avatar_url,
          // Add other Profile fields if they are selected in getPromptByShortId's user select
        }
      : { // Fallback if user object is not directly available
          id: data.user_id, // user_id from prompts table
          username: "Anonymous",
        },
    
    author: data.user?.username || "Anonymous", // Legacy, try to use user object

    tags: Array.isArray(data.tags)
      ? data.tags.map((t: any) => t.tag || t).filter(Boolean) // Handle potential nesting (tag:tags(*))
      : [],

    originalPromptId: data.original_prompt_id,
    originalPrompt: data.originalPrompt || null, // Add original prompt details

    // --- STATS ---
    // These come from the 'stats' object (which is from the 'prompt_statistics' view)
    // OR directly from the 'prompts' table if 'stats' is not available (as a fallback)

    rating: stats?.rating ?? data.rating ?? 0, // Net score (upvotes - downvotes)
    upvotes: stats?.upvotes ?? 0,               // Actual upvotes count
    // For consistency in PromptDetailView's sidebar, map 'upvotes' to 'likeCount'
    likeCount: stats?.upvotes ?? 0,
    // downvotes: stats?.downvotes ?? 0, // You might want this too
    commentCount: stats?.comment_count ?? 0,
    remixCount: stats?.remix_count ?? 0,

    // view_count comes directly from the 'prompts' table, not 'prompt_statistics'
    viewCount: data.view_count ?? 0,

    createdAt: data.created_at,
    updatedAt: data.updated_at,
    isPremium: data.is_premium, // Assuming this field exists on prompts table
    relatedPrompts: data.relatedPrompts || [], // If you fetch these separately

    // AI Model data
    ai_model_id: data.ai_model_id,
    ai_model: data.ai_model_id && data.ai_model
      ? {
          id: data.ai_model.id,
          provider: data.ai_model.provider,
          name: data.ai_model.tool_name, // This is the specific model name
          slug: data.ai_model.slug,
          deprecated: data.ai_model.deprecated,
          type: data.ai_model.type,
        }
      : null,
  };
}

/**
 * Transform prompt_card_details view to frontend PromptCard
 */
export function transformPromptCard(dbPromptCard: any): PromptCard {
  if (!dbPromptCard.short_id) {
    console.warn(`[transformPromptCard] WARNING: dbPromptCard.short_id is falsy for id=${dbPromptCard.id}, title=${dbPromptCard.title}`);
  }
  return {
    id: dbPromptCard.id,
    shortId: dbPromptCard.short_id,
    short_id: dbPromptCard.short_id, // Preserve original snake_case field for debugging
    title: dbPromptCard.title,
    description: dbPromptCard.description,
    imageUrl: dbPromptCard.image_url,
    createdAt: dbPromptCard.created_at,
    updatedAt: dbPromptCard.updated_at,
    isPublic: dbPromptCard.is_public,
    viewCount: dbPromptCard.view_count,
    category: {
      name: dbPromptCard.category_name,
      slug: dbPromptCard.category_slug,
    },
    tool: dbPromptCard.tool_name
      ? {
          name: dbPromptCard.tool_name,
          slug: dbPromptCard.tool_slug,
        }
      : undefined,
    user: {
      id: dbPromptCard.author_id,
      username: dbPromptCard.author_username,
      avatarUrl: dbPromptCard.author_avatar_url,
    },
    tags: Array.isArray(dbPromptCard.tags) ? dbPromptCard.tags : [],
    primary_tag_slug: dbPromptCard.primary_tag_slug,
    rating: dbPromptCard.rating || 0,
    likeCount: dbPromptCard.rating || 0, // Map rating to likeCount for consistency with PromptCard component
    commentCount: dbPromptCard.comment_count || 0,
    remixCount: dbPromptCard.remix_count || 0,
    trendingScore: dbPromptCard.trending_score || 0,

    // AI Model data
    ai_model_id: dbPromptCard.ai_model_id,
    ai_model: dbPromptCard.ai_model_id
      ? {
          id: dbPromptCard.ai_model_id,
          provider: dbPromptCard.ai_model_provider,
          name: dbPromptCard.ai_model_name,
          slug: dbPromptCard.ai_model_slug,
          deprecated: dbPromptCard.ai_model_deprecated,
          // type: dbPromptCard.ai_model_type, // Add if type is included in prompt_card_details
        }
      : null,
  }
}

/**
 * Transform prompt_card_details with saved status to frontend PromptCard
 */
export function transformPromptCardWithSaved(data: any): PromptCard {
  // Start with the base transformation
  const basePrompt = transformPromptCard(data);

  // Add the saved status with debugging
  const isSaved = data.is_saved_by_user || false;

  // Debug logging for saved status
  if (data.is_saved_by_user === true) {
    console.log('[transformPromptCardWithSaved] Found saved prompt:', {
      id: data.id,
      title: data.title,
      is_saved_by_user: data.is_saved_by_user,
      isSaved
    });
  }

  const result = {
    ...basePrompt,
    isSaved,
  };

  return result;
}

export function transformCategory(data: any): Category {
  return {
    id: Number(data.id), // Ensure ID is a number
    name: data.name,
    slug: data.slug,
    description: data.description,
    imagePath: data.image_path || data.imagePath, // Handle both potential field names
    promptCount: data.prompt_count || data.promptCount || 0, // Handle both potential field names
  };
}

export function transformTool(data: any): Tool {
  return {
    id: Number(data.id), // Ensure ID is a number
    name: data.name,
    slug: data.slug,
    icon: data.icon,
    description: data.description,
    website: data.website,
    promptCount: data.prompt_count || data.promptCount || 0, // Handle both potential field names
  };
}

export function transformTag(data: any): any { // Add export and define return type
  return data; // Placeholder, implement transformation logic
}

export function transformProfile(data: any): any { // Add export and define return type
  return data; // Placeholder, implement transformation logic
}

export function transformCollection(data: any): Collection {
  // Validate input data
  if (!data) {
    // Create a minimal valid collection to prevent UI errors
    return {
      id: 'temp-' + Date.now(),
      userId: '',
      name: 'Temporary Collection',
      description: '',
      icon: '',
      isPublic: false,
      isDefault: false,
      defaultType: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      promptCount: 0,
      viewCount: 0
    };
  }
  
  // Generate fallback values for missing fields
  const fallbackId = data.id || ('temp-' + Date.now());
  const fallbackName = data.name || `Collection ${fallbackId}`;
  
  const transformed = {
    id: fallbackId,
    userId: data.user_id || '',
    name: fallbackName,
    description: data.description || '',
    icon: data.icon || '', // Now directly contains the image URL
    isPublic: data.is_public || false,
    isDefault: data.is_default || false,
    defaultType: data.default_type || null,
    createdAt: data.created_at || new Date().toISOString(),
    updatedAt: data.updated_at || new Date().toISOString(),
    promptCount: data.prompt_count || 0,
    viewCount: data.view_count || 0,
    user: data.user ? {
      username: data.user.username || '',
      avatar_url: data.user.avatar_url || ''
    } : undefined,
    prompts: data.prompts ? data.prompts.map(transformPromptCard) : undefined
  };
  
  return transformed;
}
