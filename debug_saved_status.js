// Debug script to test saved status in the browser console
// Run this in the browser console on a page with prompt cards

console.log('=== DEBUGGING SAVED STATUS ===');

// Check if user is authenticated
const userElement = document.querySelector('[data-user-id]');
const userId = userElement?.getAttribute('data-user-id');
console.log('User ID from DOM:', userId);

// Check if PromptCard components have the correct saved state
const promptCards = document.querySelectorAll('[data-prompt-id]');
console.log('Found prompt cards:', promptCards.length);

promptCards.forEach((card, index) => {
  const promptId = card.getAttribute('data-prompt-id');
  const bookmarkButton = card.querySelector('button[title*="Save"], button[title*="Manage"]');
  const isBookmarkFilled = bookmarkButton?.querySelector('svg')?.classList.contains('fill-current');
  const isBookmarkGreen = bookmarkButton?.classList.contains('bg-accent-green');
  
  console.log(`Card ${index + 1}:`, {
    promptId,
    hasBookmarkButton: !!bookmarkButton,
    bookmarkTitle: bookmarkButton?.getAttribute('title'),
    isBookmarkFilled,
    isBookmarkGreen,
    bookmarkClasses: bookmarkButton?.className
  });
});

// Check if there are any console errors related to saved status
console.log('Check the Network tab for API calls to get_prompts_unified');
console.log('Look for currentUserId parameter in the API calls');
