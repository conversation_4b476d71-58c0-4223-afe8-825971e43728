"use client";

import { useState, useEffect } from "react";
import { getPrompts } from "@/lib/api-services";
import { useUser } from "@/lib/hooks/use-user";
import type { PromptCard } from "@/lib/types";

export default function DebugSavedPage() {
  const { user, isLoading: userLoading } = useUser();
  const [prompts, setPrompts] = useState<PromptCard[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any[]>([]);

  const fetchPrompts = async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      console.log("[DebugSaved] Fetching prompts for user:", user.id);
      
      const data = await getPrompts({
        currentUserId: user.id,
        limit: 10,
        offset: 0,
        sortBy: "created_at",
        sortOrder: "desc"
      });
      
      console.log("[DebugSaved] Received prompts:", data);
      console.log("[DebugSaved] Saved prompts:", data.filter(p => p.isSaved));

      // Capture debug info
      const savedPrompts = data.filter(p => p.isSaved);
      const debugData = [
        `Total prompts: ${data.length}`,
        `Saved prompts: ${savedPrompts.length}`,
        `User ID: ${user.id}`,
        `Saved prompt IDs: ${savedPrompts.map(p => p.id).join(', ') || 'None'}`,
        `All prompt saved status: ${data.map(p => `${p.title.substring(0, 30)}... = ${p.isSaved}`).join('; ')}`
      ];
      setDebugInfo(debugData);

      setPrompts(data);
    } catch (err) {
      console.error("[DebugSaved] Error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!userLoading && user?.id) {
      fetchPrompts();
    }
  }, [user?.id, userLoading]);

  if (userLoading) {
    return <div className="p-8">Loading user...</div>;
  }

  if (!user) {
    return <div className="p-8">Please sign in to debug saved status.</div>;
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Saved Status</h1>
      
      <div className="mb-4">
        <p><strong>User ID:</strong> {user.id}</p>
        <p><strong>Username:</strong> {user.profile?.username || "N/A"}</p>
      </div>

      <button
        onClick={fetchPrompts}
        disabled={isLoading}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {isLoading ? "Loading..." : "Refresh Prompts"}
      </button>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error}
        </div>
      )}

      {debugInfo.length > 0 && (
        <div className="mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          <h3 className="font-semibold mb-2">Debug Info:</h3>
          {debugInfo.map((info, index) => (
            <div key={index} className="text-sm">{info}</div>
          ))}
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Prompts ({prompts.length})</h2>
        
        {prompts.map((prompt) => (
          <div
            key={prompt.id}
            className={`p-4 border rounded ${prompt.isSaved ? 'bg-green-50 border-green-300' : 'bg-gray-50 border-gray-300'}`}
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium">{prompt.title}</h3>
                <p className="text-sm text-gray-600">{prompt.description}</p>
                <p className="text-xs text-gray-500">ID: {prompt.id}</p>
              </div>
              <div className="text-right">
                <div className={`px-2 py-1 rounded text-sm ${prompt.isSaved ? 'bg-green-200 text-green-800' : 'bg-gray-200 text-gray-800'}`}>
                  {prompt.isSaved ? 'SAVED' : 'NOT SAVED'}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Author: {prompt.user?.username || 'Unknown'}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
